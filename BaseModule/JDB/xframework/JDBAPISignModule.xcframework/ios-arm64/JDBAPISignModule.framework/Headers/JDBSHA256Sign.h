//
//  JDBSHA256Sign.h
//  JDBAPISignModule
//
//  Created by Leven on 2018/3/19.
//

#import <Foundation/Foundation.h>


@interface JDBSHA256Sign : NSObject


/**
 网关需求,加密key=value排序后的字符串；
 ！注意key值不要传nil

 @param contents  key:value 的字典
 @param key key 加密的key，不要传nil
 @return 返回加密后的sign, key为nil时返回值为@""
 */
+ (NSString *_Nullable)hmacSha256:(NSDictionary *_Nonnull)contents withKey:(NSString *_Nonnull)key;


@end
