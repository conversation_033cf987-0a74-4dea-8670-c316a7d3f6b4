//
//  JDBAPISignModule.h
//
//
//

/*
 头文件中不需要暴露，或者引入任何资源
 */

@interface JDBAPISignModule : NSObject

/*
输出一个JDBAPISignModule组件exportInterface接口, 查看JDRouter.h头文件方法说明
 
 arg:signDic 为 NSDictionary
 
 @"secretkey" 为秘钥的key,value 为秘钥
 
 router://JDBAPISignModule/hmacSha256
 NSString *signValue = [JDRouter openURL:@"router://JDBAPISignModule/hmacSha256"
 arg:signDic
 error:nil
 completion:nil];
*/


@end


